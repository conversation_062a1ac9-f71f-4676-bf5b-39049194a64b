import fetch, { IFetchOptions } from '@alife/amap-fetch';
import { message } from 'antd';
import { uuid } from '@alife/kb-biz-util';
import { isArray } from '@alife/amap-mp-utils';

const request = async (url: string, data: any = {}, options: IFetchOptions = {}) => {
  try {
    const res = await fetch({
      apiKey: url, // 云鼎网关以及在新轩辕环境下请求的老接口走这个
      params: isArray(data)
        ? data
        : {
            ...data,
            requestId: uuid(),
          },
      options,
    });
    if (res.success) {
      return Promise.resolve(res.data || {});
    } else {
      // @ts-ignore customResponse
      const errorMessage = res.resultMsg || res.message || res.errorMessage || '请求失败';
      console.error('API请求失败详情:', {
        url,
        params: data,
        response: res,
        errorMessage
      });
      message.error(errorMessage);
      return Promise.reject(new Error(errorMessage));
    }
  } catch (err) {
    const errorMessage = err.message || err.errorMessage || '请求失败';
    message.error(errorMessage);
    return Promise.reject(new Error(errorMessage));
  }
};
export default request;

export const gdRequest = async (url: string, data: any = {}, options: IFetchOptions = {}) => {
  try {
    const res = await fetch({
      params: {
        action: url, // 高德商家网关/后续新接口都走这个，新轩辕可以都走这个，老轩辕请求高德商家网关接口也用这个
        bizContent: {
          ...data,
          requestId: uuid(),
        },
      },
      options,
      pid: data?.pid || undefined,
    });
    if (Object.prototype.hasOwnProperty.call(res.data || {}, 'success')) {
      if (res.data.success) {
        return Promise.resolve(res.data?.data || {});
      } else {
        // @ts-ignore customResponse
        const errorMessage =
          res.data.msgInfo || res.data.errorMessage || res.data.message || '请求失败';
        message.error(errorMessage);
        return Promise.reject(new Error(errorMessage));
      }
    }
    if (res.success) {
      return Promise.resolve(res.data?.data || res.data || {});
    } else {
      // @ts-ignore customResponse
      const errorMessage = res.resultMsg || res.message || res.errorMessage || '请求失败';
      message.error(errorMessage);
      return Promise.reject(new Error(errorMessage));
    }
  } catch (err) {
    const errorMessage = err.message || err.errorMessage || '请求失败';
    message.error(errorMessage);
    return Promise.reject(new Error(errorMessage));
  }
};
