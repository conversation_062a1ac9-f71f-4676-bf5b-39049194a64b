import React, { useState, useCallback, useEffect } from 'react';
import { Modal, Steps, Button } from 'antd';
import { BatchSubmitStep, IShopInfo, ITargetShopSetting } from '@/types/batch-submit';
import ShopSelector from './shop-selector';
import SubmitReview from './submit-review';
import { traceExp, traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import { queryUnfinishedOptWoosBizOrder } from '@/services/batch-submit';

const BatchSubmitModal: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState<BatchSubmitStep>(BatchSubmitStep.SHOP_SELECT);
  const [selectedShops, setSelectedShops] = useState<IShopInfo[]>([]);
  const [targetShopSettings, setTargetShopSettings] = useState<ITargetShopSetting[]>([]);
  const [batchBtnDisabled, setBatchBtnDisabled] = useState(false);

  // 查询未完结批量提报任务
  useEffect(() => {
    // 暂时注释掉未完结工单查询，先让门店列表查询正常工作
    // queryUnfinishedOptWoosBizOrder({}).then((res) => {
    //   setBatchBtnDisabled(res.result === true);
    // }).catch((e) => {
    //   console.error('查询未完结工单失败:', e);
    //   setBatchBtnDisabled(false); // 查询失败时允许使用批量提报功能
    // });
    setBatchBtnDisabled(false); // 临时设置为可用状态

    // 挂载时曝光埋点
    traceExp(PageSPMKey.首页, ModuleSPMKey['门店列表.批量提报按钮'], {});
  }, []);

  // 弹窗曝光埋点
  useEffect(() => {
    if (modalVisible) {
      traceExp(PageSPMKey.首页, ModuleSPMKey['批量提报.弹窗曝光'], {});
    }
  }, [modalVisible]);
  // 步骤切换埋点
  useEffect(() => {
    if (modalVisible) {
      if (currentStep === BatchSubmitStep.SHOP_SELECT) {
        traceExp(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择步骤'], {});
      } else if (currentStep === BatchSubmitStep.SUBMIT_REVIEW) {
        traceExp(PageSPMKey.首页, ModuleSPMKey['批量提报.提报审核步骤'], {});
      }
    }
  }, [currentStep, modalVisible]);

  // 打开弹窗
  const handleOpenModal = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['门店列表.批量提报按钮'], {});
    setModalVisible(true);
  }, []);

  // 关闭弹窗时清空所有数据
  const handleClose = useCallback(() => {
    setCurrentStep(BatchSubmitStep.SHOP_SELECT);
    setSelectedShops([]);
    setTargetShopSettings([]);
    setModalVisible(false);
  }, []);

  // 步骤切换
  const handleNext = useCallback(() => {
    setCurrentStep(BatchSubmitStep.SUBMIT_REVIEW);
  }, []);
  const handlePrev = useCallback(() => {
    setCurrentStep(BatchSubmitStep.SHOP_SELECT);
  }, []);

  // 门店选择回调
  const handleShopsSelected = useCallback((shops: IShopInfo[]) => {
    setSelectedShops(shops);
  }, []);

  // 目标门店设置回调
  const handleTargetShopChange = useCallback((settings: ITargetShopSetting[]) => {
    setTargetShopSettings(settings);
  }, []);

  // 提交成功后关闭弹窗
  const handleSubmit = useCallback(() => {
    handleClose();
  }, [handleClose]);

  return (
    <>
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: 16,
          background: '#fff',
        }}
      >
        <Button type="primary" disabled={batchBtnDisabled} onClick={handleOpenModal}>
          批量提报
        </Button>
      </div>
      <Modal
        open={modalVisible}
        onCancel={handleClose}
        footer={null}
        width={1200}
        destroyOnClose
        title="批量提报"
      >
        <Steps
          current={currentStep - 1}
          items={[{ title: '门店选择' }, { title: '提报审核' }]}
          style={{ marginBottom: 24 }}
        />
        {currentStep === BatchSubmitStep.SHOP_SELECT && (
          <ShopSelector
            onShopsSelected={handleShopsSelected}
            onCancel={handleClose}
            onNext={handleNext}
          />
        )}
        {currentStep === BatchSubmitStep.SUBMIT_REVIEW && (
          <SubmitReview
            shops={selectedShops}
            onTargetShopChange={handleTargetShopChange}
            onCancel={handleClose}
            onPrev={handlePrev}
            onSubmit={handleSubmit}
          />
        )}
      </Modal>
    </>
  );
};

export default BatchSubmitModal;
