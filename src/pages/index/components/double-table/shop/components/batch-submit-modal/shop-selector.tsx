import React, { useState, useEffect, useCallback } from 'react';
import { Table, Button, Form, Input, Select, DatePicker, Space, message } from 'antd';
import { getBatchSubmitShopList } from '@/services/batch-submit';
import { IBatchSubmitShopListParams, IShopInfo, IShopFilterConditions } from '@/types/batch-submit';
import dayjs from 'dayjs';
import { traceClick, traceExp, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

interface IShopSelectorProps {
  onShopsSelected: (shops: IShopInfo[]) => void;
  onCancel: () => void;
  onNext: () => void;
}

const { RangePicker } = DatePicker;

const ShopSelector: React.FC<IShopSelectorProps> = ({ onShopsSelected, onCancel, onNext }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [shopList, setShopList] = useState<IShopInfo[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedShops, setSelectedShops] = useState<IShopInfo[]>([]);
  const [pageInfo, setPageInfo] = useState({ pageNo: 1, pageSize: 10, totalCount: 0 });
  const [filters, setFilters] = useState<IShopFilterConditions>({});

  // 查询门店列表
  const fetchShopList = useCallback(
    async (params?: Partial<IBatchSubmitShopListParams>) => {
      setLoading(true);
      try {
        // console.log('发起API请求，参数:', {
        //   page: { pageNo: pageInfo.pageNo, pageSize: pageInfo.pageSize },
        //   source: 'BASIC_SHOP_LIST',
        //   ...filters,
        //   ...params,
        // });

        const res = await getBatchSubmitShopList({
          page: { pageNo: pageInfo.pageNo, pageSize: pageInfo.pageSize },
          source: 'BASIC_SHOP_LIST',
          ...filters,
          ...params,
        } as IBatchSubmitShopListParams);

        // console.log('API响应数据:', res);
        // console.log('dataList:', res.dataList);
        // console.log('pageInfo:', res.pageInfo);

        if (!res || typeof res !== 'object') {
          throw new Error('API返回数据格式错误');
        }

        setShopList(res.dataList || []);
        setPageInfo((prev) => ({ ...prev, totalCount: res.pageInfo?.totalCount || 0 }));
      } catch (e) {
        console.error('门店列表获取失败:', e);
        message.error('门店列表获取失败');
      } finally {
        setLoading(false);
      }
    },
    [filters, pageInfo.pageNo, pageInfo.pageSize],
  );

  useEffect(() => {
    fetchShopList();
  }, [fetchShopList]);

  // 筛选埋点
  const handleSearch = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择.筛选'], {});
    form.validateFields().then((values) => {
      const {
        pid,
        shopId,
        hasHistoricalReport,
        isHistoricalOrderApproved,
        isMerchantScoreQualified,
        lastSubmitTime,
      } = values;
      setFilters({
        pid,
        shopId,
        hasHistoricalReport,
        isHistoricalOrderApproved,
        isMerchantScoreQualified,
        lastSubmitStartTime:
          lastSubmitTime && lastSubmitTime[0]
            ? dayjs(lastSubmitTime[0]).format('YYYY-MM-DD HH:mm:ss')
            : undefined,
        lastSubmitEndTime:
          lastSubmitTime && lastSubmitTime[1]
            ? dayjs(lastSubmitTime[1]).format('YYYY-MM-DD HH:mm:ss')
            : undefined,
      });
      setPageInfo((prev) => ({ ...prev, pageNo: 1 }));
    });
  }, [form]);

  // 重置列表
  const handleReset = useCallback(() => {
    form.resetFields();
    setFilters({});
    setPageInfo((prev) => ({ ...prev, pageNo: 1 }));
  }, [form]);

  // 分页切换
  const handleTableChange = useCallback(
    (pagination) => {
      setPageInfo({
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
        totalCount: pageInfo.totalCount,
      });
    },
    [pageInfo.totalCount],
  );

  // 选择门店埋点
  const handleSelectChange = useCallback(
    (keys: React.Key[], rows: IShopInfo[]) => {
      traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择.选择门店'], {
        count: keys.length,
      });
      setSelectedRowKeys(keys);
      // 合并已选门店，去重
      const allSelected = [...selectedShops, ...rows].filter(
        (shop, idx, arr) => arr.findIndex((s) => s.shopId === shop.shopId) === idx,
      );
      setSelectedShops(allSelected.filter((shop) => keys.includes(shop.shopId)));
    },
    [selectedShops],
  );

  // 全选/取消全选埋点
  const handleSelectAll = useCallback(
    (selected: boolean, selectedRows: IShopInfo[], changeRows: IShopInfo[]) => {
      traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择.选择门店'], { all: selected });
      if (selected) {
        const all = [...selectedShops, ...changeRows].filter(
          (shop, idx, arr) => arr.findIndex((s) => s.shopId === shop.shopId) === idx,
        );
        setSelectedShops(all);
        setSelectedRowKeys(all.map((shop) => shop.shopId));
      } else {
        const remain = selectedShops.filter(
          (shop) => !changeRows.some((row) => row.shopId === shop.shopId),
        );
        setSelectedShops(remain);
        setSelectedRowKeys(remain.map((shop) => shop.shopId));
      }
    },
    [selectedShops],
  );

  // 下一步埋点
  const handleNext = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择.下一步'], {});
    if (selectedShops.length === 0) {
      message.warning('请至少选择一个门店');
      return;
    }
    onShopsSelected(selectedShops);
    onNext();
  }, [selectedShops, onShopsSelected, onNext]);

  // 取消埋点
  const handleCancel = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择.取消'], {});
    onCancel();
  }, [onCancel]);

  // 表格列定义
  const columns = [
    { title: '门店名称', dataIndex: 'shopName', key: 'shopName' },
    { title: '门店ID', dataIndex: 'shopId', key: 'shopId' },
    { title: '商户ID', dataIndex: 'pid', key: 'pid' },
    { title: '最近提报时间', dataIndex: 'lastSubmitTime', key: 'lastSubmitTime' },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16, padding: 16, borderRadius: 6 }}>
        <Form form={form} layout="vertical">
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 16 }}>
            <Form.Item name="pid" label="商户ID" style={{ marginBottom: 16 }}>
              <Input placeholder="请输入商户ID" allowClear />
            </Form.Item>
            <Form.Item name="shopId" label="门店ID" style={{ marginBottom: 16 }}>
              <Input placeholder="请输入门店ID" allowClear />
            </Form.Item>
            <Form.Item name="hasHistoricalReport" label="历史是否提报" style={{ marginBottom: 16 }}>
              <Select allowClear placeholder="请选择">
                <Select.Option value>是</Select.Option>
                <Select.Option value={false}>否</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="isHistoricalOrderApproved"
              label="历史工单是否过审"
              style={{ marginBottom: 16 }}
            >
              <Select allowClear placeholder="请选择">
                <Select.Option value>是</Select.Option>
                <Select.Option value={false}>否</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="isMerchantScoreQualified"
              label="商家分是否达标"
              style={{ marginBottom: 16 }}
            >
              <Select allowClear placeholder="请选择">
                <Select.Option value>是</Select.Option>
                <Select.Option value={false}>否</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item name="lastSubmitTime" label="最近提报时间" style={{ marginBottom: 16 }}>
              <RangePicker
                showTime
                placeholder={['开始时间', '结束时间']}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </div>
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={handleReset}>重置</Button>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
            </Space>
          </div>
        </Form>
      </div>
      <Table
        rowKey="shopId"
        loading={loading}
        columns={columns}
        dataSource={shopList}
        pagination={{
          current: pageInfo.pageNo,
          pageSize: pageInfo.pageSize,
          total: pageInfo.totalCount,
          showSizeChanger: true,
        }}
        rowSelection={{
          selectedRowKeys,
          onChange: handleSelectChange,
          onSelectAll: handleSelectAll,
        }}
        onChange={handleTableChange}
        scroll={{ x: 1000 }}
      />
      <div
        style={{
          marginTop: 24,
          textAlign: 'right',
        }}
      >
        <Space>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={handleNext}>
            下一步
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default ShopSelector;
