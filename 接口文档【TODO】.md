# 接口文档【TODO】

# 1 新增运维工单门店信息查询接口

**时序图**

```plantuml
@startuml
autonumber
actor "端" as user
participant "gateWay" as gw

participant "amap-sales-operation-client" as client

participant "amap-sales-operation-app" as app

participant "amap-sales-operation-infra" as infra

participant "数据湖" as hu

participant "Ha3" as ha3

user -> gw: 运维工单门店信息查询
gw -> client: 查询运维工单门店信息
client -> app: 参数校验，封装请求参数
app -> app: 调用运维工单查询处理器链
note right of app
SHOP_LIST_TASK_SHOP_ENGINE_INFO 门店引擎
SHOP_LIST_OPERATOR_INFO 人店运维关系
新增：SHOP_LIST_WOOS_ORDER_INFO 历史提报工单
新增：QUERY_SHOP_COLLECT_INFO 采集友商门店信息
新增：SHOP_LIST_ORDER_TASK_RESULT_BUILD 构建返回结果
end note
app -> infra: 新增过滤条件，商家分是否达标、货架是否达标、历史是否提报、最近提报时间
infra -> ha3: 查询门店信息
ha3 -> app: 返回查询结果
app -> infra: 查询人店运维关系信息
infra -> hu: 查询人店运维关系信息
infra -> app: 返回查询结果
app -> infra: 查询历史提报工单
infra -> hu: 封装查询条件，查询历史提报工单审核状态
hu -> app: 返回查询结果
app -> app: 结果构造,设置排序规则，按最近一次提报时间倒序
app -> user: 返回查询结果
@enduml
```

**接口：**

com.amap.sales.operation.client.AgentOperationQueryFacade#queryOperationOrderShopList

**类型：**GATEWAY

**入参：【**新增字段：hasHistoricalReport（历史是否提报）、isHistoricalOrderApproved（历史工单是否过审）、isMerchantScoreQualified（商家分是否达标）、lastSubmitStartTime（最近一次提报开始时间）、lastSubmitEndTime（最近一次提报结束时间）**】**

```json
{
    "requests": [
        {
            "action": "amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationShopList",
            "param": {
                "includeShopTaskProcess": true,
                "queryType": "OptOrderShopInfo",
                "shopId": "xgc_20241009152746440jcdb|xiniu_xgc_bgc",
                "pid": "20908088000028001",
                "hasHistoricalReport": true,
                "isHistoricalOrderApproved": true,
                "isMerchantScoreQualified": true,
                "lastSubmitStartTime":  "2025-08-23 23:59:59",
                "lastSubmitEndTime":  "2025-07-22 00:00:00",
                "sortBy": "LAST_SUBMIT_TIME",
                "sortType": "desc",
                "page": {
                    "pageNo": 1,
                    "pageSize": 10
                },
                "appSource": "xy-client",
                "requestId": "f99b0b7b-6c10-4bfa-04df-5dd07d86f786",
                "source": "BASIC_SHOP_LIST"
            },
            "requestType": "msePc",
            "extraHeader": {
                "requestid": "f99b0b7b-6c10-4bfa-04df-5dd07d86f786",
                "requestsource": 7
            },
            "skipCompatGwApi": false
        }
    ]
}
```

**出参【新增字段:** lastSubmitTime、collectShopName**】：**

**TODO：商家分，质检状态，poi等字段是否需要透出**

```json
{
    "result": true,
    "code": "00000",
    "message": null,
    "version": "1.0",
    "timestamp": "1753099894907",
    "success": true,
    "msgInfo": "调用成功",
    "msgCode": "SUCCESS",
    "traceId": "0bfb408b17530998928682094e162f",
    "data": {
        "result": true,
        "traceId": "0bfb408b17530998928682094e162f",
        "code": "1",
        "data": {
            "dataList": [
                {
                    "shopName": "文明村烧烤吧",
                    "pid": "1088013502221685",
                    "shopId": "2016101900077000000019261428|xiniu_xgc_bgc",
                    "lastSubmitTime": "2025-03-27 17:49:29",
                    "collectShopName": "友商店铺名称"
                },
                {
                    "shopName": " 初雪乌龙茶馆",
                    "pid": "2089000953316963",
                    "shopId": "xgc_202408311445285508qxd|xiniu_xgc_bgc",
                    "lastSubmitTime": "2025-05-23 10:49:29",
                    "collectShopName": "友商店铺名称"
                }
            ],
            "pageInfo": {
                "totalPage": 15,
                "hasMore": true,
                "pageSize": 10,
                "nextPageNo": 2,
                "totalCount": 143,
                "currentPageNo": 1
            }
        },
        "success": true,
        "message": null,
        "msgInfo": "调用成功",
        "version": "1.0",
        "msgCode": "SUCCESS",
        "timestamp": "1753099894900"
    }
}
```

**备注 ：**    

# 2 新增批量提报未完结流水查询接口

**时序图**

```plantuml
@startuml
autonumber
actor "端" as user
participant "gateWay" as gw

participant "amap-sales-operation-client" as client

participant "amap-sales-operation-app" as app

participant "amap-sales-operation-infra" as infra

participant "amap-sales-operation-db" as db

user -> gw: 批量提报结果查询
gw -> client: 批量提报结果查询接口
client -> app: 参数校验，封装查询请求
app -> infra: 查询批量提报结果
infra -> db: 根据任务类型、任务状态、操作人id查询bizorder表 \nbiz_type=OPT_ESP_ORDER_BATCH_CREATE_PROCESS status=DOING operator=小二id
db -> app: 返回查询结果
app -> app: 解析完成情况
app -> user: 返回查询结果
@enduml
```

**接口：**

com.amap.sales.operation.client.ShopInfraManageFacade#queryUnfinishedOptWoosBizOrder

**类型：**GATEWAY

**入参：**

```json
[
  {
    "commonOperatorInfo": {
      "amapUid": "",
      "sellerId": "",
      "operatorNickName": "",
      "operatorType": "",
      "partnerId": "",
      "comId": "",
      "source": "",
      "class": "com.amap.sales.operation.client.common.CommonOperatorInfo",
      "operatorId": "3033000000008440",
      "operatorName": "",
      "workId": ""
    },
    "gatewaySource": "",
    "requestId": "",
    "class": "com.amap.sales.operation.client.request.OptEspOrderBatchSubmitRequest"
  }
]
```

**出参：**

```json
{
  "result": true,
  "traceId": "0b9fdf3a17530973767927716e185f",
  "code": "1",
  "data": {
    "result": false,
    "bizOrders": [
      {
        "bizOrderId": "************",
        "taskType": "OPT_ESP_ORDER_BATCH_CREATE_PROCESS",
        "status": "DOING",
        "createTime": "2025-01-07 10:30:00",
        "updateTime": "2025-01-07 10:35:00",
        "operatorId": "3033000000008440",
        "operatorName": "张三",
        "extInfo": {
          "totalCount": 50,
          "successCount": 30,
          "failCount": 20,
          "shopList": [
            {
              "shopId": "xgc_20240729113748123jim5|xiniu_xgc_bgc",
              "shopName": "几何面包测试店(余杭一店)",
              "status": "SUCCESS",
              "message": "提报成功"
            }
          ]
        }
      }
    ],
    "class": "com.amap.sales.operation.client.dto.OptEspOrderResultInfoDTO"
  },
  "success": true,
  "message": null,
  "class": "com.autonavi.openplatform.commons.result.ResultDTO",
  "msgInfo": "调用成功",
  "version": "1.0",
  "msgCode": "SUCCESS",
  "timestamp": "1753097383101"
}
```

**注意：** 根据代码中的实际使用，前端直接使用 `res.result` 字段，而不是 `res.data.result`。这里的 `data.result` 是后端返回的实际业务数据。

**出参字段说明：**

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| result | Boolean | 是 | 是否有未完结工单：true(表示有未完结工单)，false(表示没有未完结工单) |
| bizOrders | Array | 否 | 工单审核流水列表，当result为true时返回 |
| bizOrders[].bizOrderId | String | 是 | 工单流水号 |
| bizOrders[].taskType | String | 是 | 任务类型，固定值：OPT_ESP_ORDER_BATCH_CREATE_PROCESS |
| bizOrders[].status | String | 是 | 任务状态：DOING(进行中)、SUCCESS(成功)、FAIL(失败) |
| bizOrders[].createTime | String | 是 | 创建时间，格式：yyyy-MM-dd HH:mm:ss |
| bizOrders[].updateTime | String | 是 | 更新时间，格式：yyyy-MM-dd HH:mm:ss |
| bizOrders[].operatorId | String | 是 | 操作人ID |
| bizOrders[].operatorName | String | 是 | 操作人姓名 |
| bizOrders[].extInfo | Object | 否 | 扩展信息，包含批量提报的详细信息 |
| bizOrders[].extInfo.totalCount | Number | 否 | 总提报数量 |
| bizOrders[].extInfo.successCount | Number | 否 | 成功提报数量 |
| bizOrders[].extInfo.failCount | Number | 否 | 失败提报数量 |
| bizOrders[].extInfo.shopList | Array | 否 | 门店提报详情列表 |
| bizOrders[].extInfo.shopList[].shopId | String | 是 | 门店ID |
| bizOrders[].extInfo.shopList[].shopName | String | 是 | 门店名称 |
| bizOrders[].extInfo.shopList[].status | String | 是 | 提报状态：SUCCESS(成功)、FAIL(失败) |
| bizOrders[].extInfo.shopList[].message | String | 否 | 提报结果说明 |

**业务逻辑说明：**

1. **result字段判断逻辑：**
   - `result: true` - 表示当前操作人存在未完结的批量提报工单
   - `result: false` - 表示当前操作人没有未完结的批量提报工单

2. **前端处理逻辑：**
   - 当 `result` 为 `false` 时，前端可以弹出新的批量提报窗口页面
   - 当 `result` 为 `true` 时，前端应该显示现有的未完结工单信息，不允许创建新的批量提报任务

3. **bizOrders字段说明：**
   - 工单审核流水，包含当前操作人所有未完结的批量提报任务
   - 每个工单包含任务进度、门店提报详情等完整信息
   - 可用于前端展示任务进度和结果

**代码使用示例：**

```typescript
// 查询未完结批量提报任务
queryUnfinishedOptWoosBizOrder({}).then((res) => {
  // res.result: true 表示有未完结工单，禁用批量提报按钮
  // res.result: false 表示没有未完结工单，允许创建新的批量提报任务
  setBatchBtnDisabled(res.result === true);
});
```

# 3 新增批量提报任务创建接口

**时序图**

```plantuml
@startuml
autonumber
actor "端" as user
participant "gateWay" as gw

participant "amap-sales-operation-app" as app

participant "amap-sales-operation-infra" as infra

participant "amap-sales-operation-db" as db

user -> gw: 批量提报任务创建
gw -> app: 参数校验，封装处理器请求
app -> app: 封装批量创建任务对象
app -> infra: 封装任务号、创建和修改时间
infra -> db: 将任务插入数据库common_task表 \ntaskType=OPT_ESP_ORDER_BATCH_CREATE,提报数据存extInfo中
db -> app: 返回任务号
app -> user: 返回创建结果
@enduml
```

**接口：**

com.amap.sales.operation.client.ShopInfraManageFacade#batchSubmitAndCreateEspOrder

**类型：**GATEWAY

**入参：**

```json
[
  {
    "commonOperatorInfo": {
      "amapUid": "",
      "sellerId": "",
      "operatorNickName": "",
      "operatorType": "INNER",
      "partnerId": "",
      "comId": "",
      "source": "",
      "class": "com.amap.sales.operation.client.common.CommonOperatorInfo",
      "operatorId": "3033000000008440",
      "operatorName": "",
      "workId": ""
    },
    "gatewaySource": "",
    "requestId": "9dc3066d-48c5-9160-30a3-0fe2054b46bf",
    "createReqs": [
      {
        "commonOperatorInfo": {
          "class": "com.amap.sales.operation.client.common.CommonOperatorInfo"
        },
        "gatewaySource": "",
        "requestId": "",
        "collectShopName": "几何面包测试店(余杭一店)",
        "shopId": "xgc_20240729113748123jim5|xiniu_xgc_bgc",
        "class": "com.amap.sales.operation.client.request.OptEspOrderCreateReq"
      },
      {
        "commonOperatorInfo": {
          "class": "com.amap.sales.operation.client.common.CommonOperatorInfo"
        },
        "gatewaySource": "",
        "requestId": "",
        "collectShopName": "壹号大宅装饰",
        "shopId": "xgc_20240813104152669hcpp|xiniu_xgc_bgc",
        "class": "com.amap.sales.operation.client.request.OptEspOrderCreateReq"
      }
    ],
    "class": "com.amap.sales.operation.client.request.OptEspOrderBatchCreateReq"
  }
]
```

**出参：**

```json
{
  "result": true,
  "traceId": "0b9fe12017530964279085362e1927",
  "code": "1",
  "data": null,
  "success": true,
  "message": null,
  "class": "com.autonavi.openplatform.commons.result.ResultDTO",
  "msgInfo": "调用成功",
  "version": "1.0",
  "msgCode": "SUCCESS",
  "timestamp": "1753096438648"
}
```

**备注 ：**